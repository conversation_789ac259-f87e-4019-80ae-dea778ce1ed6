package content

import (
	"contentmanager/infrastructure/database/driver"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/pkgs/auth"
	"contentmanager/pkgs/reservation"
	"encoding/json"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"gorm.io/datatypes"
	"time"
)

type (
	Base struct {
		auth.SharableBase
		PrivacyLevel int
		PublishPeriod
	}

	PublishPeriod struct {
		PublishAt *time.Time
		ExpireAt  *time.Time
	}

	Content struct {
		ID uuid.UUID `gorm:"column:id;type:uuid;primary_key;NOT NULL;DEFAULT:uuid_generate_v4()"`
		Base
		reservation.Reservable
		Active      bool
		Type        commonModels.ContentType
		Owner       uuid.UUID
		Publisher   uuid.UUID
		Title       string
		Content     string
		Data        json.RawMessage
		Structure   json.RawMessage
		Route       string
		Path        string
		PageLayout  commonModels.PageType `gorm:"column:pagelayout;type:page_style;DEFAULT:'HTML'::page_style;NOT NULL"`
		Created     time.Time
		Updated     time.Time
		Deleted     *time.Time
		Approved    bool
		MediaID     *uuid.UUID
		Settings    json.RawMessage
		StructureID *uuid.UUID
		Meta        datatypes.JSONType[map[string]string]
		Structures  driver.PgUUIDArray `gorm:"type:uuid[]"`
		Tags        driver.PgUUIDArray `gorm:"type:uuid[]"`
	}
)

func (c Content) GetResourceContainers() (result []string) {
	if !c.Active {
		return result
	}

	if c.Type == "template" || c.Type == "distributed_page" {
		result = append(result, c.Content)
	} else {
		result = append(result, string(c.Data))
	}
	if c.MediaID != nil {
		result = append(result, fmt.Sprintf("'/images/%s'", c.MediaID.String()))
	}
	return
}

func (b Content) GetID() uuid.UUID {
	return b.ID
}

func (b Content) GetType() string {
	return b.Type.String()
}

func (b Content) GetRoute() string {
	return b.Route
}

func (Content) TableName() string {
	return "content"
}

func (b Content) GetScopeEntity() string {
	switch b.Type {
	case commonModels.Template, commonModels.CSS, commonModels.JS:
		return "cm.resource." + string(b.Type)
	case commonModels.Page, commonModels.DistributedPage, commonModels.ExternalLinkContentType:
		return "cm.content.page"
	case commonModels.Fragment:
		return "cm.fragment"
	default:
		return "cm.content." + string(b.Type)
	}
}

func (b Base) GetSites() []uuid.UUID {
	return b.Sites
}

func (b Base) GetDepartmentID() *uuid.UUID {
	return b.DepartmentID
}

func (p PublishPeriod) IsPublished(now time.Time) bool {
	if p.PublishAt == nil {
		return false
	}

	return p.PublishAt.Before(now) && (p.ExpireAt == nil || p.ExpireAt.After(now))
}

// PublishStatus returns 'draft' | 'published' | 'scheduled' | 'expired'
func (p PublishPeriod) PublishStatus(now time.Time) string {
	if p.PublishAt == nil {
		return "draft"
	}

	if p.ExpireAt != nil && p.ExpireAt.Before(now) {
		return "expired"
	}

	if p.PublishAt.After(now) {
		return "scheduled"
	}

	return "published"
}

func (p PublishPeriod) GetPublished() bool {
	return p.PublishAt != nil
}

func (c Content) GetSettingsMap() map[string]interface{} {
	var result map[string]interface{}
	json.Unmarshal(c.Settings, &result)
	return result
}

func (c Content) GetUrlPaths() []string {
	return []string{
		fmt.Sprintf("/go/%s*", c.ID),
		c.Route,
	}
}

var _ commonModels.IContentRoute = (*Content)(nil)
